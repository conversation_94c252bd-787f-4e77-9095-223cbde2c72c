Dependencies for Project 'D00094A_FW_VA', Target 'D00094A_FW_VA': (DO NOT MODIFY !)
F (../Src/main.c)(0x680DFF21)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\main.o --omf_browse .\debx-08112a-va\main.crf --depend .\debx-08112a-va\main.d)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Inc/usb_device.h)(0x6587F42D)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Inc/gpio.h)(0x6560CF38)
I (../Inc/mdio.h)(0x65ED6F1C)
I (../Inc/dac.h)(0x6560D3E3)
I (../Inc/adc.h)(0x6560CF3A)
I (../Inc/dma.h)(0x6560CF3A)
I (../Inc/rng.h)(0x657D6230)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (../Inc/bcm87800.h)(0x66E83717)
I (../Inc/master_i2c.h)(0x674028ED)
I (../Inc/master_i2c_mod.h)(0x66A5ADF7)
I (../Inc/master_i2c_pm.h)(0x66A5BC5F)
I (../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc/usbd_cdc.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
I (../Inc/usbd_cdc_if.h)(0x671F9359)
I (../Inc/clock-Freq-Registers.h)(0x6677DE2D)
I (../Inc/usart.h)(0x657D8352)
F (../Src/gpio.c)(0x675A5E41)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\gpio.o --omf_browse .\debx-08112a-va\gpio.crf --depend .\debx-08112a-va\gpio.d)
I (../Inc/gpio.h)(0x6560CF38)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Src/usb_device.c)(0x6587F402)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usb_device.o --omf_browse .\debx-08112a-va\usb_device.crf --depend .\debx-08112a-va\usb_device.d)
I (../Inc/usb_device.h)(0x6587F42D)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
I (../Inc/usbd_desc.h)(0x655CBD0A)
I (../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc/usbd_cdc.h)(0x641F30E9)
I (../Inc/usbd_cdc_if.h)(0x671F9359)
F (../Src/usbd_conf.c)(0x6587F402)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usbd_conf.o --omf_browse .\debx-08112a-va\usbd_conf.crf --depend .\debx-08112a-va\usbd_conf.d)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
F (../Src/usbd_desc.c)(0x672B0AB9)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usbd_desc.o --omf_browse .\debx-08112a-va\usbd_desc.crf --depend .\debx-08112a-va\usbd_desc.d)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
I (../Inc/usbd_desc.h)(0x655CBD0A)
F (../Src/stm32f2xx_it.c)(0x671F9318)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_it.o --omf_browse .\debx-08112a-va\stm32f2xx_it.crf --depend .\debx-08112a-va\stm32f2xx_it.d)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Inc/usart.h)(0x657D8352)
F (../Src/stm32f2xx_hal_msp.c)(0x6560CF3C)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_msp.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_msp.crf --depend .\debx-08112a-va\stm32f2xx_hal_msp.d)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Src\master_i2c.c)(0x680E010D)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\master_i2c.o --omf_browse .\debx-08112a-va\master_i2c.crf --depend .\debx-08112a-va\master_i2c.d)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Inc/master_i2c.h)(0x674028ED)
F (..\Src\mdio.c)(0x673741C5)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\mdio.o --omf_browse .\debx-08112a-va\mdio.crf --depend .\debx-08112a-va\mdio.d)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Inc/mdio.h)(0x65ED6F1C)
F (..\Src\usbd_cdc_if.c)(0x671F9467)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usbd_cdc_if.o --omf_browse .\debx-08112a-va\usbd_cdc_if.crf --depend .\debx-08112a-va\usbd_cdc_if.d)
I (../Inc/usbd_cdc_if.h)(0x671F9359)
I (../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc/usbd_cdc.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
F (..\Src\bcm87800.c)(0x680DF96B)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\bcm87800.o --omf_browse .\debx-08112a-va\bcm87800.crf --depend .\debx-08112a-va\bcm87800.d)
I (../Inc/bcm87800.h)(0x66E83717)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\diag_fec_statistics_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_power_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_chip_wrapper.h)(0x655CBD0A)
I (..\api\capi\inc\host_download_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_to_chip_ipc.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
I (..\api\capi\inc\capi_test.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\capi\inc\capi_diag.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_diag_def.h)(0x655CBD0A)
I (../Inc/master_i2c.h)(0x674028ED)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Src\rng.c)(0x65EDADA6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\rng.o --omf_browse .\debx-08112a-va\rng.crf --depend .\debx-08112a-va\rng.d)
I (../Inc/rng.h)(0x657D6230)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Src\dac.c)(0x66A5FBBD)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\dac.o --omf_browse .\debx-08112a-va\dac.crf --depend .\debx-08112a-va\dac.d)
I (../Inc/dac.h)(0x6560D3E3)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Src\dma.c)(0x6720589E)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\dma.o --omf_browse .\debx-08112a-va\dma.crf --depend .\debx-08112a-va\dma.d)
I (../Inc/dma.h)(0x6560CF3A)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Src\adc.c)(0x66F58117)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\adc.o --omf_browse .\debx-08112a-va\adc.crf --depend .\debx-08112a-va\adc.d)
I (../Inc/adc.h)(0x6560CF3A)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Src\master_i2c_pm.c)(0x673EAF71)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\master_i2c_pm.o --omf_browse .\debx-08112a-va\master_i2c_pm.crf --depend .\debx-08112a-va\master_i2c_pm.d)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Inc/master_i2c_pm.h)(0x66A5BC5F)
F (..\Src\usart.c)(0x67611E52)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usart.o --omf_browse .\debx-08112a-va\usart.crf --depend .\debx-08112a-va\usart.d)
I (../Inc/usart.h)(0x657D8352)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Inc/usbd_cdc_if.h)(0x671F9359)
I (../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc/usbd_cdc.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_pcd_ex.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_pcd_ex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_pcd_ex.crf --depend .\debx-08112a-va\stm32f2xx_hal_pcd_ex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_pcd.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_pcd.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_pcd.crf --depend .\debx-08112a-va\stm32f2xx_hal_pcd.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_ll_usb.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_ll_usb.o --omf_browse .\debx-08112a-va\stm32f2xx_ll_usb.crf --depend .\debx-08112a-va\stm32f2xx_ll_usb.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_adc.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_adc.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_adc.crf --depend .\debx-08112a-va\stm32f2xx_hal_adc.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_adc_ex.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_adc_ex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_adc_ex.crf --depend .\debx-08112a-va\stm32f2xx_hal_adc_ex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_i2c.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_i2c.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_i2c.crf --depend .\debx-08112a-va\stm32f2xx_hal_i2c.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_spi.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_spi.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_spi.crf --depend .\debx-08112a-va\stm32f2xx_hal_spi.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_tim.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_tim.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_tim.crf --depend .\debx-08112a-va\stm32f2xx_hal_tim.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_tim_ex.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_tim_ex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_tim_ex.crf --depend .\debx-08112a-va\stm32f2xx_hal_tim_ex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal.o --omf_browse .\debx-08112a-va\stm32f2xx_hal.crf --depend .\debx-08112a-va\stm32f2xx_hal.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_rcc.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_rcc.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_rcc.crf --depend .\debx-08112a-va\stm32f2xx_hal_rcc.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_rcc_ex.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_rcc_ex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_rcc_ex.crf --depend .\debx-08112a-va\stm32f2xx_hal_rcc_ex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_cortex.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_cortex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_cortex.crf --depend .\debx-08112a-va\stm32f2xx_hal_cortex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_flash.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_flash.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_flash.crf --depend .\debx-08112a-va\stm32f2xx_hal_flash.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_flash_ex.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_flash_ex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_flash_ex.crf --depend .\debx-08112a-va\stm32f2xx_hal_flash_ex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_gpio.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_gpio.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_gpio.crf --depend .\debx-08112a-va\stm32f2xx_hal_gpio.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_dma.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_dma.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_dma.crf --depend .\debx-08112a-va\stm32f2xx_hal_dma.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Drivers/STM32F2xx_HAL_Driver/Src/stm32f2xx_hal_dma_ex.c)(0x641F30E6)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_dma_ex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_dma_ex.crf --depend .\debx-08112a-va\stm32f2xx_hal_dma_ex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_dac.c)(0x628BAAE4)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_dac.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_dac.crf --depend .\debx-08112a-va\stm32f2xx_hal_dac.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_dac_ex.c)(0x628BAAE4)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_dac_ex.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_dac_ex.crf --depend .\debx-08112a-va\stm32f2xx_hal_dac_ex.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_rng.c)(0x657D5A16)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_rng.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_rng.crf --depend .\debx-08112a-va\stm32f2xx_hal_rng.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..\Drivers\STM32F2xx_HAL_Driver\Src\stm32f2xx_hal_uart.c)(0x621C255E)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\stm32f2xx_hal_uart.o --omf_browse .\debx-08112a-va\stm32f2xx_hal_uart.crf --depend .\debx-08112a-va\stm32f2xx_hal_uart.d)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (..//Src/system_stm32f2xx.c)(0x641F30E0)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\system_stm32f2xx.o --omf_browse .\debx-08112a-va\system_stm32f2xx.crf --depend .\debx-08112a-va\system_stm32f2xx.d)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_core.c)(0x641F30E9)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usbd_core.o --omf_browse .\debx-08112a-va\usbd_core.crf --depend .\debx-08112a-va\usbd_core.d)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ctlreq.c)(0x641F30E9)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usbd_ctlreq.o --omf_browse .\debx-08112a-va\usbd_ctlreq.crf --depend .\debx-08112a-va\usbd_ctlreq.d)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
F (../Middlewares/ST/STM32_USB_Device_Library/Core/Src/usbd_ioreq.c)(0x641F30E9)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usbd_ioreq.o --omf_browse .\debx-08112a-va\usbd_ioreq.crf --depend .\debx-08112a-va\usbd_ioreq.d)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
F (..\Middlewares\ST\STM32_USB_Device_Library\Class\CDC\Src\usbd_cdc.c)(0x641F30E9)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\usbd_cdc.o --omf_browse .\debx-08112a-va\usbd_cdc.crf --depend .\debx-08112a-va\usbd_cdc.d)
I (../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc/usbd_cdc.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ioreq.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_def.h)(0x641F30E9)
I (../Inc/usbd_conf.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f2xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/stm32f205xx.h)(0x641F30E0)
I (../Drivers/CMSIS/Include/core_cm3.h)(0x641F30E5)
I (C:\Keil_v5\ARM\ARMCC\include\stdint.h)(0x5475F300)
I (../Drivers/CMSIS/Include/core_cmInstr.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/cmsis_armcc.h)(0x641F30E5)
I (../Drivers/CMSIS/Include/core_cmFunc.h)(0x641F30E5)
I (../Drivers/CMSIS/Device/ST/STM32F2xx/Include/system_stm32f2xx.h)(0x641F30E0)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal.h)(0x641F30E6)
I (../Inc/stm32f2xx_hal_conf.h)(0x671F90F1)
I (../Inc/main.h)(0x67E7B007)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_def.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy/stm32_hal_legacy.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rcc_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_gpio_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dma_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_cortex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_adc_ex.h)(0x5B9DEA40)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_dac_ex.h)(0x628BAAE4)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_flash_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_i2c.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pwr_ex.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_rng.h)(0x657D5A16)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_spi.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_uart.h)(0x5FAF9A50)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_ll_usb.h)(0x641F30E6)
I (../Drivers/STM32F2xx_HAL_Driver/Inc/stm32f2xx_hal_pcd_ex.h)(0x641F30E6)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_core.h)(0x641F30E9)
I (../Middlewares/ST/STM32_USB_Device_Library/Core/Inc/usbd_ctlreq.h)(0x641F30E9)
I (../Inc/usbd_desc.h)(0x655CBD0A)
F (.\startup_stm32f205xx.s)(0x66992BF4)(--cpu Cortex-M3 -g --apcs=interwork --pd "__MICROLIB SETA 1"

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

--pd "__UVISION_VERSION SETA 514" --pd "STM32F205xx SETA 1"

--list .\debx-08112a-va\startup_stm32f205xx.lst --xref -o .\debx-08112a-va\startup_stm32f205xx.o --depend .\debx-08112a-va\startup_stm32f205xx.d)
F (..\api\capi\src\capi.c)(0x65ED7855)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\capi.o --omf_browse .\debx-08112a-va\capi.crf --depend .\debx-08112a-va\capi.d)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_config_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\ipc_regs.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_power_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_chip_wrapper.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_lw_wrapper.h)(0x655CBD0A)
I (..\api\capi\inc\host_download_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_to_chip_ipc.h)(0x655CBD0A)
I (..\api\capi\inc\host_gpio_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_avs.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
F (..\api\capi\src\capi_custom.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\capi_custom.o --omf_browse .\debx-08112a-va\capi_custom.crf --depend .\debx-08112a-va\capi_custom.d)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_custom_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_utils.h)(0x655CBD0A)
I (..\api\capi\inc\host_lw_wrapper.h)(0x655CBD0A)
I (..\api\capi\inc\capi_custom.h)(0x655CBD0A)
I (..\api\chip\indep\inc\lw_common_config_ind.h)(0x655CBD0A)
F (..\api\capi\src\capi_diag.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\capi_diag.o --omf_browse .\debx-08112a-va\capi_diag.crf --depend .\debx-08112a-va\capi_diag.d)
I (..\api\chip\indep\inc\chip_config_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_fw_intf.h)(0x655CBD0A)
I (..\api\capi\inc\host_to_chip_ipc.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_diag_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag.h)(0x655CBD0A)
I (..\api\capi\inc\capi_diag.h)(0x655CBD0A)
I (..\api\chip\indep\inc\diag_fec_statistics_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_fec_statistics.h)(0x655CBD0A)
F (..\api\capi\src\capi_test.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\capi_test.o --omf_browse .\debx-08112a-va\capi_test.crf --depend .\debx-08112a-va\capi_test.d)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\chip_config_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
I (..\api\capi\inc\host_test.h)(0x655CBD0A)
I (..\api\capi\inc\host_fec_prbs.h)(0x655CBD0A)
I (..\api\capi\inc\capi_test.h)(0x655CBD0A)
I (..\api\capi\inc\host_lw_wrapper.h)(0x655CBD0A)
F (..\api\capi\src\host_avs.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_avs.o --omf_browse .\debx-08112a-va\host_avs.crf --depend .\debx-08112a-va\host_avs.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_avs.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
F (..\api\capi\src\host_chip_wrapper.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_chip_wrapper.o --omf_browse .\debx-08112a-va\host_chip_wrapper.crf --depend .\debx-08112a-va\host_chip_wrapper.d)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\lw_common_config_ind.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_chip_wrapper.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_utils.h)(0x655CBD0A)
F (..\api\capi\src\host_diag.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_diag.o --omf_browse .\debx-08112a-va\host_diag.crf --depend .\debx-08112a-va\host_diag.d)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x5475F2FE)
I (C:\Keil_v5\ARM\ARMCC\include\float.h)(0x5475F300)
I (..\api\chip\indep\inc\chip_config_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_fw_intf.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\indep\inc\lw_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\indep\inc\fw_event_log.h)(0x655CBD0A)
I (..\api\capi\inc\host_to_chip_ipc.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_diag_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_utils.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
F (..\api\capi\src\host_diag_fec_statistics.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_diag_fec_statistics.o --omf_browse .\debx-08112a-va\host_diag_fec_statistics.crf --depend .\debx-08112a-va\host_diag_fec_statistics.d)
I (C:\Keil_v5\ARM\ARMCC\include\math.h)(0x5475F2FE)
I (..\api\chip\indep\inc\chip_config_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\capi\inc\capi_test.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\diag_fec_statistics_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_fec_statistics.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_test.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_modes.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_xbar.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
F (..\api\capi\src\host_diag_util.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_diag_util.o --omf_browse .\debx-08112a-va\host_diag_util.crf --depend .\debx-08112a-va\host_diag_util.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_config_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_chip_wrapper.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\capi\inc\hw_mutex_handler.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\capi\inc\host_to_chip_ipc.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_utils.h)(0x655CBD0A)
F (..\api\capi\src\host_fec_prbs.c)(0x6581C45C)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_fec_prbs.o --omf_browse .\debx-08112a-va\host_fec_prbs.crf --depend .\debx-08112a-va\host_fec_prbs.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\string.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x5475F300)
I (..\api\chip\indep\inc\chip_config_def.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_diag_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\diag_fec_statistics_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\cw_comm_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\pam4_prbs_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_kp4prbs.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_modes.h)(0x655CBD0A)
I (..\api\capi\inc\host_fec_prbs.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clkrst_control.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clockrst_mux.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_status_check.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_datapath_cfg.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_top.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_handler.h)(0x655CBD0A)
I (..\api\capi\inc\host_diag_fec_statistics.h)(0x655CBD0A)
F (..\api\capi\src\host_gpio_util.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_gpio_util.o --omf_browse .\debx-08112a-va\host_gpio_util.crf --depend .\debx-08112a-va\host_gpio_util.d)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_gpio_util.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_gpio.h)(0x655CBD0A)
F (..\api\capi\src\host_log_util.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_log_util.o --omf_browse .\debx-08112a-va\host_log_util.crf --depend .\debx-08112a-va\host_log_util.d)
I (C:\Keil_v5\ARM\ARMCC\include\time.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x5475F2FA)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
F (..\api\capi\src\host_lw_wrapper.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_lw_wrapper.o --omf_browse .\debx-08112a-va\host_lw_wrapper.crf --depend .\debx-08112a-va\host_lw_wrapper.d)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_lw_wrapper.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_utils.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\indep\inc\lw_common_config_ind.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
F (..\api\capi\src\host_power_util.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_power_util.o --omf_browse .\debx-08112a-va\host_power_util.crf --depend .\debx-08112a-va\host_power_util.d)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_power_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\capi\inc\capi.h)(0x655CBD0A)
I (..\api\capi\inc\host_avs.h)(0x655CBD0A)
F (..\api\capi\src\host_test.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_test.o --omf_browse .\debx-08112a-va\host_test.crf --depend .\debx-08112a-va\host_test.d)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_test_def.h)(0x655CBD0A)
I (..\api\capi\inc\host_test.h)(0x655CBD0A)
F (..\api\capi\src\host_to_chip_ipc.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_to_chip_ipc.o --omf_browse .\debx-08112a-va\host_to_chip_ipc.crf --depend .\debx-08112a-va\host_to_chip_ipc.d)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\capi\inc\host_log_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_fw_intf.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\host_chip_mem_map.h)(0x655CBD0A)
I (..\api\capi\inc\host_to_chip_ipc.h)(0x655CBD0A)
F (..\api\capi\src\hw_mutex_handler.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\hw_mutex_handler.o --omf_browse .\debx-08112a-va\hw_mutex_handler.crf --depend .\debx-08112a-va\hw_mutex_handler.d)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\capi\inc\hw_mutex_handler.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_prbs.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_prbs.o --omf_browse .\debx-08112a-va\chal_cw_prbs.crf --depend .\debx-08112a-va\chal_cw_prbs.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\pam4_prbs_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_prbs.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_rtmr_clkrst_control.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_rtmr_clkrst_control.o --omf_browse .\debx-08112a-va\chal_cw_rtmr_clkrst_control.crf --depend .\debx-08112a-va\chal_cw_rtmr_clkrst_control.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_modes.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_status_check.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clkrst_control.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_rtmr_clockrst_mux.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_rtmr_clockrst_mux.o --omf_browse .\debx-08112a-va\chal_cw_rtmr_clockrst_mux.crf --depend .\debx-08112a-va\chal_cw_rtmr_clockrst_mux.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_modes.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clockrst_mux.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_rtmr_datapath_cfg.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_rtmr_datapath_cfg.o --omf_browse .\debx-08112a-va\chal_cw_rtmr_datapath_cfg.crf --depend .\debx-08112a-va\chal_cw_rtmr_datapath_cfg.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_modes.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_datapath_cfg.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_rtmr_kp4prbs.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_rtmr_kp4prbs.o --omf_browse .\debx-08112a-va\chal_cw_rtmr_kp4prbs.crf --depend .\debx-08112a-va\chal_cw_rtmr_kp4prbs.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\pam4_prbs_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_prbs.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_kp4prbs.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clkrst_control.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clockrst_mux.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_rtmr_status_check.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_rtmr_status_check.o --omf_browse .\debx-08112a-va\chal_cw_rtmr_status_check.crf --depend .\debx-08112a-va\chal_cw_rtmr_status_check.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_status_check.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_rtmr_xdec.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_rtmr_xdec.o --omf_browse .\debx-08112a-va\chal_cw_rtmr_xdec.crf --depend .\debx-08112a-va\chal_cw_rtmr_xdec.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_xdec.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_rtmr_xenc.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_rtmr_xenc.o --omf_browse .\debx-08112a-va\chal_cw_rtmr_xenc.crf --depend .\debx-08112a-va\chal_cw_rtmr_xenc.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_xenc.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_top.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_top.o --omf_browse .\debx-08112a-va\chal_cw_top.crf --depend .\debx-08112a-va\chal_cw_top.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_utils.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_top.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_cw_utils.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_cw_utils.o --omf_browse .\debx-08112a-va\chal_cw_utils.crf --depend .\debx-08112a-va\chal_cw_utils.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_utils.h)(0x655CBD0A)
F (..\api\chip\dep\chal\src\chal_gpio.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chal_gpio.o --omf_browse .\debx-08112a-va\chal_gpio.crf --depend .\debx-08112a-va\chal_gpio.d)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_gpio.h)(0x655CBD0A)
F (..\api\chip\dep\common\src\chip_config.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\chip_config.o --omf_browse .\debx-08112a-va\chip_config.crf --depend .\debx-08112a-va\chip_config.d)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
F (..\api\chip\dep\common\src\dsp_config.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\dsp_config.o --omf_browse .\debx-08112a-va\dsp_config.crf --depend .\debx-08112a-va\dsp_config.d)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_config.h)(0x655CBD0A)
F (..\api\chip\dep\common\src\dsp_utils.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\dsp_utils.o --omf_browse .\debx-08112a-va\dsp_utils.crf --depend .\debx-08112a-va\dsp_utils.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_utils.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_config.h)(0x655CBD0A)
F (..\api\chip\dep\common\src\host_chip_mem_map.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\host_chip_mem_map.o --omf_browse .\debx-08112a-va\host_chip_mem_map.crf --depend .\debx-08112a-va\host_chip_mem_map.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\dep\common\inc\host_chip_mem_map.h)(0x655CBD0A)
F (..\api\chip\dep\ml\src\ml_cw_rtmr_handler.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\ml_cw_rtmr_handler.o --omf_browse .\debx-08112a-va\ml_cw_rtmr_handler.crf --depend .\debx-08112a-va\ml_cw_rtmr_handler.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\indep\inc\cw_comm_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_handler.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_modes.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_xbar.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clkrst_control.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_clockrst_mux.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_datapath_cfg.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_status_check.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_top.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_xdec.h)(0x655CBD0A)
I (..\api\chip\dep\chal\inc\chal_cw_rtmr_xenc.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\dsp_config.h)(0x655CBD0A)
I (..\api\chip\indep\inc\fw_event_log.h)(0x655CBD0A)
F (..\api\chip\dep\ml\src\ml_cw_rtmr_modes.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\ml_cw_rtmr_modes.o --omf_browse .\debx-08112a-va\ml_cw_rtmr_modes.crf --depend .\debx-08112a-va\ml_cw_rtmr_modes.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_xbar.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_rtmr_modes.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\hw_chip_common_def_dep.h)(0x655CBD0A)
F (..\api\chip\dep\ml\src\ml_cw_xbar.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\ml_cw_xbar.o --omf_browse .\debx-08112a-va\ml_cw_xbar.crf --depend .\debx-08112a-va\ml_cw_xbar.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\chip_common_config_ind.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\chip_mode_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\capi_def.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\cw_def.h)(0x655CBD0A)
I (..\api\chip\dep\ml\inc\ml_cw_xbar.h)(0x655CBD0A)
F (..\api\chip\indep\src\common_util.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\common_util.o --omf_browse .\debx-08112a-va\common_util.crf --depend .\debx-08112a-va\common_util.d)
I (C:\Keil_v5\ARM\ARMCC\include\stdio.h)(0x5475F300)
I (C:\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x5475F300)
I (..\api\chip\indep\inc\regs_common.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_fw_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\portofino_rtmr_pp_regs.h)(0x655CBD0A)
I (..\api\chip\dep\common\inc\fw_gp_reg_map.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\common_util.h)(0x655CBD0A)
F (..\api\platform\utils\src\hr_time.c)(0x655CBD0A)(--c99 -c --cpu Cortex-M3 -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I../Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc -I../Drivers/STM32F2xx_HAL_Driver/Inc/Legacy -I../Middlewares/ST/STM32_USB_Device_Library/Core/Inc -I../Middlewares/ST/STM32_USB_Device_Library/Class/CDC/Inc -I../Drivers/CMSIS/Device/ST/STM32F2xx/Include -I../Drivers/CMSIS/Include -I..\api -I..\api\capi\inc -I..\api\chip\dep\chal\inc -I..\api\chip\dep\common\inc -I..\api\chip\dep\ml\inc -I..\api\chip\indep\inc -I..\api\platform\reg_access\inc -I..\api\platform\utils\inc

-I E:\000work\E8106Fbt\D00094A_FW_VA_high_20241118_QDD\D00094A_FW_VA_high_OSFP_V1.84\MDK-ARM\RTE

-I C:\Keil_v5\ARM\PACK\Keil\STM32F2xx_DFP\2.9.0

-I C:\Keil_v5\ARM\CMSIS\Include

-D__UVISION_VERSION="514" -DSTM32F205xx -DUSE_HAL_DRIVER -DSTM32F205xx

-o .\debx-08112a-va\hr_time.o --omf_browse .\debx-08112a-va\hr_time.crf --depend .\debx-08112a-va\hr_time.d)
I (..\api\chip\indep\inc\common_def.h)(0x655CBD0A)
I (..\api\chip\indep\inc\type_defns.h)(0x655CBD0A)
I (..\api\chip\indep\inc\access.h)(0x655CBD0A)
I (..\api\platform\reg_access\inc\reg_access.h)(0x655CBD0A)
I (..\api\platform\utils\inc\hr_time.h)(0x655CBD0A)
