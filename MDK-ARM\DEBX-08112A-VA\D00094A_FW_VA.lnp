--cpu Cortex-M3
".\debx-08112a-va\main.o"
".\debx-08112a-va\gpio.o"
".\debx-08112a-va\usb_device.o"
".\debx-08112a-va\usbd_conf.o"
".\debx-08112a-va\usbd_desc.o"
".\debx-08112a-va\stm32f2xx_it.o"
".\debx-08112a-va\stm32f2xx_hal_msp.o"
".\debx-08112a-va\master_i2c.o"
".\debx-08112a-va\mdio.o"
".\debx-08112a-va\usbd_cdc_if.o"
".\debx-08112a-va\bcm87800.o"
".\debx-08112a-va\rng.o"
".\debx-08112a-va\dac.o"
".\debx-08112a-va\dma.o"
".\debx-08112a-va\adc.o"
".\debx-08112a-va\master_i2c_pm.o"
".\debx-08112a-va\usart.o"
".\debx-08112a-va\stm32f2xx_hal_pcd_ex.o"
".\debx-08112a-va\stm32f2xx_hal_pcd.o"
".\debx-08112a-va\stm32f2xx_ll_usb.o"
".\debx-08112a-va\stm32f2xx_hal_adc.o"
".\debx-08112a-va\stm32f2xx_hal_adc_ex.o"
".\debx-08112a-va\stm32f2xx_hal_i2c.o"
".\debx-08112a-va\stm32f2xx_hal_spi.o"
".\debx-08112a-va\stm32f2xx_hal_tim.o"
".\debx-08112a-va\stm32f2xx_hal_tim_ex.o"
".\debx-08112a-va\stm32f2xx_hal.o"
".\debx-08112a-va\stm32f2xx_hal_rcc.o"
".\debx-08112a-va\stm32f2xx_hal_rcc_ex.o"
".\debx-08112a-va\stm32f2xx_hal_cortex.o"
".\debx-08112a-va\stm32f2xx_hal_flash.o"
".\debx-08112a-va\stm32f2xx_hal_flash_ex.o"
".\debx-08112a-va\stm32f2xx_hal_gpio.o"
".\debx-08112a-va\stm32f2xx_hal_dma.o"
".\debx-08112a-va\stm32f2xx_hal_dma_ex.o"
".\debx-08112a-va\stm32f2xx_hal_dac.o"
".\debx-08112a-va\stm32f2xx_hal_dac_ex.o"
".\debx-08112a-va\stm32f2xx_hal_rng.o"
".\debx-08112a-va\stm32f2xx_hal_uart.o"
".\debx-08112a-va\system_stm32f2xx.o"
".\debx-08112a-va\usbd_core.o"
".\debx-08112a-va\usbd_ctlreq.o"
".\debx-08112a-va\usbd_ioreq.o"
".\debx-08112a-va\usbd_cdc.o"
".\debx-08112a-va\startup_stm32f205xx.o"
".\debx-08112a-va\capi.o"
".\debx-08112a-va\capi_custom.o"
".\debx-08112a-va\capi_diag.o"
".\debx-08112a-va\capi_test.o"
".\debx-08112a-va\host_avs.o"
".\debx-08112a-va\host_chip_wrapper.o"
".\debx-08112a-va\host_diag.o"
".\debx-08112a-va\host_diag_fec_statistics.o"
".\debx-08112a-va\host_diag_util.o"
".\debx-08112a-va\host_fec_prbs.o"
".\debx-08112a-va\host_gpio_util.o"
".\debx-08112a-va\host_log_util.o"
".\debx-08112a-va\host_lw_wrapper.o"
".\debx-08112a-va\host_power_util.o"
".\debx-08112a-va\host_test.o"
".\debx-08112a-va\host_to_chip_ipc.o"
".\debx-08112a-va\hw_mutex_handler.o"
".\debx-08112a-va\chal_cw_prbs.o"
".\debx-08112a-va\chal_cw_rtmr_clkrst_control.o"
".\debx-08112a-va\chal_cw_rtmr_clockrst_mux.o"
".\debx-08112a-va\chal_cw_rtmr_datapath_cfg.o"
".\debx-08112a-va\chal_cw_rtmr_kp4prbs.o"
".\debx-08112a-va\chal_cw_rtmr_status_check.o"
".\debx-08112a-va\chal_cw_rtmr_xdec.o"
".\debx-08112a-va\chal_cw_rtmr_xenc.o"
".\debx-08112a-va\chal_cw_top.o"
".\debx-08112a-va\chal_cw_utils.o"
".\debx-08112a-va\chal_gpio.o"
".\debx-08112a-va\chip_config.o"
".\debx-08112a-va\dsp_config.o"
".\debx-08112a-va\dsp_utils.o"
".\debx-08112a-va\host_chip_mem_map.o"
".\debx-08112a-va\ml_cw_rtmr_handler.o"
".\debx-08112a-va\ml_cw_rtmr_modes.o"
".\debx-08112a-va\ml_cw_xbar.o"
".\debx-08112a-va\common_util.o"
".\debx-08112a-va\hr_time.o"
--library_type=microlib --strict --scatter ".\DEBX-08112A-VA\D00094A_FW_VA.sct"
--summary_stderr --info summarysizes --map --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\DEBX-08112A-VA\D00094A_FW_VA.map" -o .\DEBX-08112A-VA\D00094A_FW_VA.axf