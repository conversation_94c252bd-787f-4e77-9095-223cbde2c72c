.\debx-08112a-va\host_gpio_util.o: ..\api\capi\src\host_gpio_util.c
.\debx-08112a-va\host_gpio_util.o: ..\api\chip\indep\inc\access.h
.\debx-08112a-va\host_gpio_util.o: ..\api\platform\reg_access\inc\reg_access.h
.\debx-08112a-va\host_gpio_util.o: ..\api\chip\indep\inc\type_defns.h
.\debx-08112a-va\host_gpio_util.o: ..\api\chip\indep\inc\common_def.h
.\debx-08112a-va\host_gpio_util.o: ..\api\chip\dep\common\inc\chip_mode_def.h
.\debx-08112a-va\host_gpio_util.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\debx-08112a-va\host_gpio_util.o: ..\api\chip\indep\inc\capi_def.h
.\debx-08112a-va\host_gpio_util.o: ..\api\capi\inc\host_gpio_util.h
.\debx-08112a-va\host_gpio_util.o: ..\api\chip\dep\chal\inc\chal_gpio.h
